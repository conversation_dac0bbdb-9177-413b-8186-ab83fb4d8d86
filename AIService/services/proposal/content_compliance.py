import asyncio
from typing import Any, Dict, List, Optional

from loguru import logger
from services.chroma.chroma_service import ChromaService
from utils.llm import KontratarLLM
from database import get_kontratar_db
from pydantic import BaseModel, Field
from langchain_ollama import ChatOllama
from enum import Enum

class VolumeTitle(str, Enum):
    VOLUME_I = "Volume I"
    VOLUME_II = "Volume II"
    VOLUME_III = "Volume III"
    VOLUME_IV = "Volume IV"
    VOLUME_V = "Volume V"
    RFI = "RFI"  # If you want to allow "RFI" as a valid value


class ContentCompliance(BaseModel):
    content: str = Field(description="The expected content to be seen in the response for this volume")
    volume_title: VolumeTitle = Field(description="The volume that is expected to be seen in the response eg. Volume I, Volume II, all the way to Volume V. It cannot be beyond these these volumes")

class ContentComplianceResponse(BaseModel):
    content_compliance: List[ContentCompliance]


class ContentComplianceService:
    """
    Service for generating content compliance context and LLM output using ChromaDB and an LLM.
    """
    def __init__(
        self,
        embedding_api_url: str = "http://ai.kontratar.com:5000",
        llm_api_url: str = "http://ai.kontratar.com:11434",
    ):
        self.max_tokens = 1024
        self.chroma_service = ChromaService(embedding_api_url, None)
        self.llm = ChatOllama(
            model="gemma3:27b",
            num_ctx = 3072,
            num_predict=self.max_tokens,
            temperature=0,
            base_url=llm_api_url,
            timeout=60 
        )

    async def generate_content_compliance(
        self,
        opportunity_id: str,
        tenant_id: str,
        source: str,
        is_rfp: bool = True,
    ) -> Dict[str, Any]:
        """
        Generate content compliance output using ChromaDB and LLM.
        Returns a dict with 'content' (LLM output) and 'context' (list of cleaned chunks).
        """
        logger.info(f"Starting generate_content_compliance for opportunity_id={opportunity_id}, tenant_id={tenant_id}, source={source}, is_rfp={is_rfp}")
        chroma_query = '''
            List the statements, sections, factors or tasks that the contractor should propose
            or include in their response as well as the different volumes to be submitted and the neccessary content for each volume.
            Get All Tasks/Factors needed to be shown in this response.
        '''
        
        # Initialize variables
        requirements_context = []
        context_str = ""

        async for db in get_kontratar_db():
            max_chunks = 4
            collection_name = f"{tenant_id}_{opportunity_id}" if source.lower() == "custom" else opportunity_id
            logger.debug(f"Querying ChromaDB with collection_name={collection_name}, chroma_query={chroma_query.strip()}, max_chunks={max_chunks}")
            relevant_chunks = await self.chroma_service.get_relevant_chunks(
                db, collection_name, chroma_query, n_results=max_chunks
            )
            requirements_context = [chunk.replace("\n", " ").replace("\t", " ") for chunk in relevant_chunks]

            max_context_length = 3000
            context_str = "\n\n".join(requirements_context)
            if len(context_str) > max_context_length:
                context_str = context_str[:max_context_length] + "..."
                logger.warning(f"Context truncated from {len('\n\n'.join(requirements_context))} to {len(context_str)} characters")
            logger.debug(f"Retrieved {len(requirements_context)} relevant context chunks for content compliance.")

            if len(requirements_context) == 0:
                logger.error("QUALITY CRITICAL: No context retrieved for content compliance - this will severely degrade proposal quality")
                raise RuntimeError("Content compliance generation failed: No context available from ChromaDB. This would result in poor quality proposals.")
            elif len(requirements_context) < 2:
                logger.warning(f"QUALITY WARNING: Only {len(requirements_context)} context chunks retrieved - proposal quality may be reduced")

            break

        logger.info(f"Context for LLM:\n{context_str}")

        message = "This is an RFP, Show me what is expected in each volume" if is_rfp else "This is an RFI"
        system_prompt = '''
            **Role:**
            You are a government contracting specialist with over 15+ years of experience drafting winning RFP and RFI responses for
            federal agencies. 
            
            **Task:**
            You task is to identify and return the NECCESSARY and REQUIRED content that should be seen in an RFP or RFI response.
            You are not creating the response itself, RATHER you are identifying the content that should be in the response.
            ALL the complete information and attachments for the RFI or RFP have been included within <context>.
            USING that information to identify the NESSESARY and REQUIRED content that must be seen in the RFI or RFP respnse.
            If it is an RFP, separate the content to be seen for each Volume.
            You will be told if this an RFI or an RFP.

            **Rules:**
            1. YOU MUST identify the required content based off the information found in <context>
            2. IF THERE is EXPLICIT mention of TASKS or FACTORS that should be responded to, PLEASE inlcude them and BE EXTEREMELY DETAILED as possible with the
            FACTORS or TASKS as they are EXTREMELY important.
            3. IF THERE is EXPLICIT mention of PERSONNEL and how they should be included in the RFP response, please INCLUDE that in your response.
            4. IF THERE are any EXPLICIT mentions of any PLANS or APPROACHES (eg. Technical, Management, Security, etc) INCLUDE them in your response.
            5. ENSURE to mention any other NECCESARY content that MUST BE included in the RFI or RFP response.
            6. This is NOT a response to be submitted, SO DO NOT write it like such.
            7. DO NOT summarize!
            8. No placeholders FOR ANY REASON
            9. DO NOT INCLUDE meta-phrases like "Based on the provided context, here is the necessary and required content"
        '''

        user_prompt = f'''
        <context>
            {context_str}
        </context>

        {message}

        USE ONLY information found on context to build the response.

        '''
        messages = [
            ("system", system_prompt),
            ("human", user_prompt)
        ]

        logger.info("Invoking LLM for content compliance...")
        try:
            content = await asyncio.wait_for(
                asyncio.to_thread(self.llm.invoke, messages),
                timeout=90.0
            )
            logger.info("LLM invocation successful for content compliance.")
        except asyncio.TimeoutError:
            logger.error("QUALITY CRITICAL: LLM timeout for content compliance - this is unacceptable for proposal quality")
            if len(context_str) > 1500:
                logger.info("QUALITY FALLBACK: Retrying with reduced context")
                reduced_context = context_str[:1500] + "..."
                reduced_user_prompt = user_prompt.replace(context_str, reduced_context)
                reduced_messages = [("system", system_prompt), ("human", reduced_user_prompt)]
                try:
                    content = await asyncio.wait_for(
                        asyncio.to_thread(self.llm.invoke, reduced_messages),
                        timeout=60.0
                    )
                    logger.warning("QUALITY FALLBACK: Content compliance generated with reduced context")
                except:
                    raise RuntimeError("QUALITY CRITICAL: Content compliance generation failed even with reduced context - cannot proceed with poor quality")
            else:
                raise RuntimeError("QUALITY CRITICAL: Content compliance generation timed out - cannot proceed with poor quality")
        except Exception as e:
            logger.error(f"QUALITY CRITICAL: LLM invocation failed for content compliance: {e}")
            raise RuntimeError(f"QUALITY CRITICAL: Failed to generate content compliance - cannot proceed with poor quality: {e}")

        logger.debug(f"LLM content compliance output: {content.content[:500]}{'...' if len(content.content) > 500 else ''}")

        content_text = content.content.strip()

        if len(content_text) < 100:
            logger.error(f"QUALITY CRITICAL: Content compliance output too short ({len(content_text)} chars) - indicates poor quality")
            raise RuntimeError("QUALITY CRITICAL: Content compliance generation produced insufficient content - cannot proceed with poor quality")

        if "no context" in content_text.lower() or "please provide" in content_text.lower():
            logger.error("QUALITY CRITICAL: Content compliance indicates missing context - this will severely degrade proposal quality")
            raise RuntimeError("QUALITY CRITICAL: Content compliance generation indicates missing context - cannot proceed with poor quality")

        if content_text.count('.') < 3:
            logger.warning("QUALITY WARNING: Content compliance output appears to lack detail")

        logger.info("QUALITY VALIDATED: Content compliance meets minimum quality standards")
        return {"content": content_text, "context": requirements_context}

    async def generate_sow_tasks(
        self,
        opportunity_id: str,
        tenant_id: str,
        source: str,
    ) -> str:
        """
        Generate specific program tasks outlined in RFP using ChromaDB and LLM.
        Returns a string which is the final output
        """
        logger.info(f"Starting generate_sow_tasks for opportunity_id={opportunity_id}, tenant_id={tenant_id}, source={source}")
        chroma_query = '''
            List the tasks/program tasks that the contractor must respond to
        '''
        

        requirements_context = []
        context_str = ""

        async for db in get_kontratar_db():
            max_chunks = 3
            collection_name = f"{tenant_id}_{opportunity_id}" if source.lower() == "custom" else opportunity_id
            logger.debug(f"Querying ChromaDB with collection_name={collection_name}, chroma_query={chroma_query.strip()}, max_chunks={max_chunks}")
            relevant_chunks = await self.chroma_service.get_relevant_chunks(
                db, collection_name, chroma_query, n_results=max_chunks
            )
            requirements_context = [chunk.replace("\n", " ").replace("\t", " ") for chunk in relevant_chunks]

            max_context_length = 2000
            context_str = "\n\n".join(requirements_context)
            if len(context_str) > max_context_length:
                context_str = context_str[:max_context_length] + "..."
                logger.warning(f"SOW context truncated from {len('\n\n'.join(requirements_context))} to {len(context_str)} characters")
            logger.debug(f"Retrieved {len(requirements_context)} relevant context chunks for SOW tasks.")
            break

        system_prompt = '''
            **Role:**
            You are a government contracting specialist with over 15+ years of experience drafting winning RFP and RFI responses for
            federal agencies. 
            
            **Task:**
            You task is to identify and return the SPECIFIC tasks/programs outlined in the RFP statement of work.
            You MUST explain EACH TASK in depth and much detail. Explain what is expected for each task to be performed successfully, subtasks if any.

            **Rules:**
            1. USE the information found in <context> to generate your answer
            2. DO NOT summarize!
            3. No placeholders FOR ANY REASON
            4. DO NOT INCLUDE meta-phrases like "Based on the provided context, here is the necessary and required content"
        '''

        user_prompt = f'''
        Return the different tasks stated in the SOW expected to be performed AS WELL as the specific activities to be done.

        <context>
            {context_str}
        </context>

        USE ONLY information found on context to build the response.
        DO NOT return anything before or after the tasks.

        '''
        messages = [
            ("system", system_prompt),
            ("user", user_prompt)
        ]
        logger.info("Invoking LLM for SOW tasks.")
        try:
            result = await asyncio.wait_for(
                asyncio.to_thread(self.llm.invoke, messages),
                timeout=90.0
            )
            logger.info("LLM invocation successful for SOW tasks.")
        except asyncio.TimeoutError:
            logger.error("LLM invocation timed out for SOW tasks")
            raise RuntimeError("SOW tasks generation timed out after 90 seconds")
        except Exception as e:
            logger.error(f"LLM invocation failed for SOW tasks: {e}")
            raise

        logger.debug(f"LLM SOW tasks output: {str(result.content)[:500]}{'...' if len(str(result.content)) > 500 else ''}")
        return str(result.content)

    async def generate(self, opportunity_id: str, tenant_id: str, source: str) -> str:
        sow_tasks = await self.generate_sow_tasks(opportunity_id, tenant_id, source)
        volumes_content = await self.generate_content_compliance(opportunity_id, tenant_id, source)

        return f"""
        {volumes_content["content"]}
        {sow_tasks}
        """

