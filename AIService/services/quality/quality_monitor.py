"""
Quality Monitoring System for Proposal Generation Pipeline

This module ensures that proposal quality is never compromised due to:
- ChromaDB timeouts or failures
- LLM timeouts or poor responses  
- Missing context or insufficient data
- Infrastructure issues

Quality is the top priority - the system will fail fast rather than produce poor quality proposals.
"""

import asyncio
from typing import List, Dict, Any, Optional
from loguru import logger
from datetime import datetime


class QualityMonitor:
    """
    Monitors and enforces quality standards throughout the proposal generation pipeline.
    """
    
    def __init__(self):
        self.quality_metrics = {
            "chromadb_failures": 0,
            "llm_timeouts": 0,
            "context_failures": 0,
            "content_quality_failures": 0,
            "total_sections_processed": 0,
            "quality_score": 100.0
        }
        self.quality_threshold = 80.0  # Minimum acceptable quality score
        
    def log_chromadb_failure(self, collection_name: str, error: str):
        """Log ChromaDB failure and assess quality impact"""
        self.quality_metrics["chromadb_failures"] += 1
        logger.error(f"QUALITY IMPACT: ChromaDB failure for {collection_name}: {error}")
        self._update_quality_score()
        
    def log_llm_timeout(self, section: str, timeout_duration: float):
        """Log LLM timeout and assess quality impact"""
        self.quality_metrics["llm_timeouts"] += 1
        logger.error(f"QUALITY IMPACT: LLM timeout for {section} after {timeout_duration}s")
        self._update_quality_score()
        
    def log_context_failure(self, section: str, context_count: int):
        """Log insufficient context and assess quality impact"""
        if context_count == 0:
            self.quality_metrics["context_failures"] += 1
            logger.error(f"QUALITY CRITICAL: Zero context retrieved for {section}")
        elif context_count < 2:
            logger.warning(f"QUALITY WARNING: Low context count ({context_count}) for {section}")
        self._update_quality_score()
        
    def validate_content_quality(self, content: str, section: str, min_length: int = 500) -> bool:
        """Validate generated content meets quality standards"""
        self.quality_metrics["total_sections_processed"] += 1
        
        # Check minimum length
        if len(content) < min_length:
            self.quality_metrics["content_quality_failures"] += 1
            logger.error(f"QUALITY FAILURE: {section} content too short ({len(content)} < {min_length} chars)")
            return False
            
        # Check for placeholder content
        placeholders = ["[", "]", "TODO", "PLACEHOLDER", "INSERT", "REPLACE"]
        for placeholder in placeholders:
            if placeholder in content.upper():
                self.quality_metrics["content_quality_failures"] += 1
                logger.error(f"QUALITY FAILURE: {section} contains placeholder: {placeholder}")
                return False
                
        # Check for generic/poor quality indicators
        poor_quality_indicators = [
            "no context",
            "please provide",
            "unable to",
            "cannot determine",
            "insufficient information"
        ]
        for indicator in poor_quality_indicators:
            if indicator in content.lower():
                self.quality_metrics["content_quality_failures"] += 1
                logger.error(f"QUALITY FAILURE: {section} contains poor quality indicator: {indicator}")
                return False
                
        # Check for sufficient detail (sentences, structure)
        sentence_count = content.count('.') + content.count('!') + content.count('?')
        if sentence_count < 5:
            logger.warning(f"QUALITY WARNING: {section} may lack detail ({sentence_count} sentences)")
            
        logger.info(f"QUALITY VALIDATED: {section} meets quality standards ({len(content)} chars, {sentence_count} sentences)")
        return True
        
    def _update_quality_score(self):
        """Update overall quality score based on failures"""
        total_operations = max(1, self.quality_metrics["total_sections_processed"])
        
        # Calculate penalty for each type of failure
        chromadb_penalty = self.quality_metrics["chromadb_failures"] * 10
        llm_penalty = self.quality_metrics["llm_timeouts"] * 15
        context_penalty = self.quality_metrics["context_failures"] * 20
        content_penalty = self.quality_metrics["content_quality_failures"] * 25
        
        total_penalty = chromadb_penalty + llm_penalty + context_penalty + content_penalty
        
        # Calculate quality score (0-100)
        self.quality_metrics["quality_score"] = max(0, 100 - (total_penalty / total_operations))
        
    def check_quality_threshold(self) -> bool:
        """Check if quality score meets minimum threshold"""
        current_score = self.quality_metrics["quality_score"]
        if current_score < self.quality_threshold:
            logger.error(f"QUALITY CRITICAL: Quality score ({current_score:.1f}) below threshold ({self.quality_threshold})")
            return False
        return True
        
    def should_stop_pipeline(self) -> bool:
        """Determine if pipeline should stop due to quality issues"""
        # Stop if too many critical failures
        if self.quality_metrics["context_failures"] > 2:
            logger.error("QUALITY CRITICAL: Too many context failures - stopping pipeline")
            return True
            
        if self.quality_metrics["llm_timeouts"] > 3:
            logger.error("QUALITY CRITICAL: Too many LLM timeouts - stopping pipeline")
            return True
            
        if self.quality_metrics["content_quality_failures"] > 1:
            logger.error("QUALITY CRITICAL: Content quality failures detected - stopping pipeline")
            return True
            
        return not self.check_quality_threshold()
        
    def get_quality_report(self) -> Dict[str, Any]:
        """Generate comprehensive quality report"""
        return {
            "timestamp": datetime.now().isoformat(),
            "quality_score": self.quality_metrics["quality_score"],
            "quality_threshold": self.quality_threshold,
            "meets_threshold": self.check_quality_threshold(),
            "metrics": self.quality_metrics.copy(),
            "recommendations": self._generate_recommendations()
        }
        
    def _generate_recommendations(self) -> List[str]:
        """Generate recommendations based on quality metrics"""
        recommendations = []
        
        if self.quality_metrics["chromadb_failures"] > 0:
            recommendations.append("Check ChromaDB server health and network connectivity")
            
        if self.quality_metrics["llm_timeouts"] > 0:
            recommendations.append("Consider reducing context size or increasing LLM timeout")
            
        if self.quality_metrics["context_failures"] > 0:
            recommendations.append("Verify RFP documents are properly uploaded and indexed")
            
        if self.quality_metrics["content_quality_failures"] > 0:
            recommendations.append("Review LLM prompts and context quality")
            
        if not recommendations:
            recommendations.append("Quality metrics are within acceptable ranges")
            
        return recommendations


# Global quality monitor instance
quality_monitor = QualityMonitor()


def get_quality_monitor() -> QualityMonitor:
    """Get the global quality monitor instance"""
    return quality_monitor


class QualityException(Exception):
    """Exception raised when quality standards are not met"""
    pass


async def ensure_quality_or_fail(operation_name: str, operation_func, *args, **kwargs):
    """
    Execute an operation and ensure it meets quality standards, or fail fast.
    This prevents the pipeline from continuing with poor quality data.
    """
    try:
        result = await operation_func(*args, **kwargs)
        
        # Validate result quality
        if isinstance(result, str) and len(result.strip()) == 0:
            raise QualityException(f"QUALITY CRITICAL: {operation_name} returned empty result")
            
        if isinstance(result, list) and len(result) == 0:
            raise QualityException(f"QUALITY CRITICAL: {operation_name} returned no data")
            
        return result
        
    except asyncio.TimeoutError:
        quality_monitor.log_llm_timeout(operation_name, 0)
        raise QualityException(f"QUALITY CRITICAL: {operation_name} timed out - cannot proceed with poor quality")
        
    except Exception as e:
        if "QUALITY CRITICAL" in str(e):
            raise  # Re-raise quality exceptions
        else:
            logger.error(f"QUALITY CRITICAL: {operation_name} failed: {e}")
            raise QualityException(f"QUALITY CRITICAL: {operation_name} failed - cannot proceed with poor quality")
